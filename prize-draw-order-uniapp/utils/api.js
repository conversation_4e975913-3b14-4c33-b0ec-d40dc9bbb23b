// API配置
const API_BASE_URL = 'http://localhost:18080' // 后台服务地址，需要根据实际情况修改

// 请求封装
const request = (options) => {
	return new Promise((resolve, reject) => {
		uni.request({
			url: API_BASE_URL + options.url,
			method: options.method || 'GET',
			data: options.data || {},
			header: {
				'Content-Type': 'application/json',
				...options.header
			},
			success: (res) => {
				if (res.statusCode === 200) {
					if (res.data.code === 200) {
						resolve(res.data)
					} else {
						uni.showToast({
							title: res.data.msg || '请求失败',
							icon: 'none'
						})
						reject(res.data)
					}
				} else {
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					})
					reject(res)
				}
			},
			fail: (err) => {
				uni.showToast({
					title: '网络连接失败',
					icon: 'none'
				})
				reject(err)
			}
		})
	})
}

// 商家相关API
export const merchantApi = {
	// 获取商家信息
	getMerchantInfo(merchantCode) {
		return request({
			url: `/api/merchant/info/${merchantCode}`
		})
	},
	
	// 获取商家配置
	getMerchantConfig(merchantCode) {
		return request({
			url: `/api/merchant/config/${merchantCode}`
		})
	},
	
	// 检查商家状态
	checkMerchantStatus(merchantCode) {
		return request({
			url: `/api/merchant/check/${merchantCode}`
		})
	}
}

// 桌台相关API
export const tableApi = {
	// 获取桌台信息
	getTableInfo(merchantCode, tableNumber) {
		return request({
			url: `/api/table/${merchantCode}/${tableNumber}`
		})
	},
	
	// 获取桌台列表
	getTableList(merchantCode) {
		return request({
			url: `/api/table/list/${merchantCode}`
		})
	},
	
	// 根据桌台ID获取信息
	getTableById(tableId) {
		return request({
			url: `/api/table/info/${tableId}`
		})
	}
}

// 抽奖相关API
export const lotteryApi = {
	// 获取当前有效活动
	getCurrentActivity(merchantCode) {
		return request({
			url: `/api/lottery/activity/${merchantCode}`
		})
	},
	
	// 获取所有有效活动
	getValidActivities(merchantCode) {
		return request({
			url: `/api/lottery/activities/${merchantCode}`
		})
	},
	
	// 检查用户是否可以参与抽奖
	checkParticipate(activityId, userOpenid) {
		return request({
			url: `/api/lottery/check/${activityId}/${userOpenid}`
		})
	},
	
	// 获取用户剩余抽奖次数
	getRemainingDraws(activityId, userOpenid) {
		return request({
			url: `/api/lottery/remaining/${activityId}/${userOpenid}`
		})
	},
	
	// 执行抽奖
	performDraw(data) {
		return request({
			url: '/api/lottery/draw',
			method: 'POST',
			data: data
		})
	},
	
	// 获取用户抽奖记录
	getUserRecords(userOpenid) {
		return request({
			url: `/api/lottery/records/${userOpenid}`
		})
	},
	
	// 获取用户在指定活动的抽奖记录
	getUserActivityRecords(activityId, userOpenid) {
		return request({
			url: `/api/lottery/records/${activityId}/${userOpenid}`
		})
	},
	
	// 获取用户中奖记录
	getUserWinningRecords(userOpenid) {
		return request({
			url: `/api/lottery/winning/${userOpenid}`
		})
	},
	
	// 获取用户未领取的中奖记录
	getUserUnclaimedRecords(userOpenid) {
		return request({
			url: `/api/lottery/unclaimed/${userOpenid}`
		})
	},
	
	// 查询用户今日抽奖次数
	getTodayDrawCount(activityId, userOpenid) {
		return request({
			url: `/api/lottery/count/today/${activityId}/${userOpenid}`
		})
	},
	
	// 查询用户总抽奖次数
	getTotalDrawCount(activityId, userOpenid) {
		return request({
			url: `/api/lottery/count/total/${activityId}/${userOpenid}`
		})
	},
	
	// 根据商家编码和桌台号获取抽奖信息
	getLotteryInfo(merchantCode, tableNumber) {
		return request({
			url: `/api/lottery/info/${merchantCode}/${tableNumber}`
		})
	}
}

// 配置相关API
export const configApi = {
	// 获取所有配置
	getAllConfig(merchantCode) {
		return request({
			url: `/api/config/${merchantCode}`
		})
	},
	
	// 获取领取说明
	getClaimInstruction(merchantCode) {
		return request({
			url: `/api/config/claim-instruction/${merchantCode}`
		})
	},
	
	// 获取扫码页面背景图片
	getScanPageBg(merchantCode) {
		return request({
			url: `/api/config/scan-bg/${merchantCode}`
		})
	},
	
	// 获取抽奖页面背景图片
	getLotteryBg(merchantCode) {
		return request({
			url: `/api/config/lottery-bg/${merchantCode}`
		})
	},
	
	// 根据配置键获取配置值
	getConfigValue(merchantCode, configKey) {
		return request({
			url: `/api/config/${merchantCode}/${configKey}`
		})
	}
}

export default {
	merchantApi,
	tableApi,
	lotteryApi,
	configApi
}
