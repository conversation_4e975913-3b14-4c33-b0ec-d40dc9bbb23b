<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>商家配置管理</span>
      </div>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本配置 -->
        <el-tab-pane label="基本配置" name="basic">
          <el-form ref="basicForm" :model="basicConfig" :rules="basicRules" label-width="120px">
            <el-form-item label="商家名称" prop="merchantName">
              <el-input v-model="basicConfig.merchantName" placeholder="请输入商家名称" />
            </el-form-item>
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="basicConfig.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="商家地址" prop="address">
              <el-input v-model="basicConfig.address" type="textarea" placeholder="请输入商家地址" />
            </el-form-item>
            <el-form-item label="微信二维码" prop="wechatQrcode">
              <image-upload v-model="basicConfig.wechatQrcode" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicConfig">保存基本配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 抽奖配置 -->
        <el-tab-pane label="抽奖配置" name="lottery">
          <el-form ref="lotteryForm" :model="lotteryConfig" label-width="120px">
            <el-form-item label="领取说明">
              <el-input v-model="lotteryConfig.claimInstruction" type="textarea" :rows="4"
                placeholder="请输入奖品领取说明，用户中奖后会看到此说明" />
            </el-form-item>
            <el-form-item label="每日抽奖次数">
              <el-input-number v-model="lotteryConfig.dailyDrawLimit" :min="1" :max="100" placeholder="每个用户每天可抽奖的次数" />
            </el-form-item>
            <el-form-item label="活动开关">
              <el-switch v-model="lotteryConfig.activityEnabled" active-text="开启" inactive-text="关闭" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveLotteryConfig">保存抽奖配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- UI配置 -->
        <el-tab-pane label="UI配置" name="ui">
          <el-form ref="uiForm" :model="uiConfig" label-width="120px">
            <el-form-item label="主题色彩">
              <el-color-picker v-model="uiConfig.primaryColor" />
            </el-form-item>
            <el-form-item label="背景图片">
              <image-upload v-model="uiConfig.backgroundImage" />
            </el-form-item>
            <el-form-item label="Logo图片">
              <image-upload v-model="uiConfig.logoImage" />
            </el-form-item>
            <el-form-item label="欢迎语">
              <el-input v-model="uiConfig.welcomeText" placeholder="请输入首页欢迎语" />
            </el-form-item>
            <el-form-item label="页面标题">
              <el-input v-model="uiConfig.pageTitle" placeholder="请输入小程序页面标题" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveUIConfig">保存UI配置</el-button>
              <el-button @click="previewUI">预览效果</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 高级配置 -->
        <el-tab-pane label="高级配置" name="advanced">
          <el-form ref="advancedForm" :model="advancedConfig" label-width="120px">
            <el-form-item label="自定义CSS">
              <el-input v-model="advancedConfig.customCss" type="textarea" :rows="8" placeholder="请输入自定义CSS样式代码" />
            </el-form-item>
            <el-form-item label="自定义JS">
              <el-input v-model="advancedConfig.customJs" type="textarea" :rows="8" placeholder="请输入自定义JavaScript代码" />
            </el-form-item>
            <el-form-item label="统计代码">
              <el-input v-model="advancedConfig.analyticsCode" type="textarea" :rows="4" placeholder="请输入第三方统计代码" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveAdvancedConfig">保存高级配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog title="UI预览" :visible.sync="previewVisible" width="400px">
      <div class="preview-container">
        <div class="preview-phone">
          <div class="preview-header" :style="{ backgroundColor: uiConfig.primaryColor }">
            <span>{{ uiConfig.pageTitle || '抽奖点餐' }}</span>
          </div>
          <div class="preview-content"
            :style="{ backgroundImage: uiConfig.backgroundImage ? `url(${uiConfig.backgroundImage})` : '' }">
            <div class="preview-logo" v-if="uiConfig.logoImage">
              <img :src="uiConfig.logoImage" alt="Logo" />
            </div>
            <div class="preview-welcome">{{ uiConfig.welcomeText || '欢迎使用抽奖点餐系统' }}</div>
            <div class="preview-buttons">
              <div class="preview-btn">点餐</div>
              <div class="preview-btn">抽奖</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMerchantConfigMapByMerchantId, batchSaveOrUpdateConfig } from "@/api/system/merchantConfig"
import { getMerchant, updateMerchant } from "@/api/system/merchant"
import { getMerchantId } from "@/api/login"
export default {
  name: "MerchantConfig",
  data() {
    return {
      activeTab: 'basic',
      merchantId: null,
      previewVisible: false,

      // 基本配置
      basicConfig: {
        merchantName: '',
        contactPhone: '',
        address: '',
        wechatQrcode: ''
      },
      basicRules: {
        merchantName: [
          { required: true, message: "商家名称不能为空", trigger: "blur" }
        ]
      },

      // 抽奖配置
      lotteryConfig: {
        claimInstruction: '请到前台出示此页面领取奖品',
        dailyDrawLimit: 3,
        activityEnabled: true
      },

      // UI配置
      uiConfig: {
        primaryColor: '#667eea',
        backgroundImage: '',
        logoImage: '',
        welcomeText: '欢迎使用抽奖点餐系统',
        pageTitle: '抽奖点餐'
      },

      // 高级配置
      advancedConfig: {
        customCss: '',
        customJs: '',
        analyticsCode: ''
      }
    }
  },

  created() {
    // 这里应该从路由参数或用户信息中获取商家ID
    this.merchantId = this.$route.params.merchantId || 1
    this.loadConfigs()
  },

  methods: {
    /** 初始化商家ID */
    initMerchantId() {
      getMerchantId().then(response => {
        if (response.data) {
          this.queryParams.merchantId = response.data;
          this.getList();
        } else {
          this.$modal.msgError("无法获取商家信息，请联系管理员");
        }
      }).catch(() => {
        this.$modal.msgError("获取商家信息失败");
      });
    },
    async loadConfigs() {
      try {
        // 加载商家基本信息
        await this.loadMerchantInfo()

        // 加载配置信息
        await this.loadMerchantConfigs()
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$modal.msgError("加载配置失败")
      }
    },

    async loadMerchantInfo() {
      const res = await getMerchant(this.merchantId)
      if (res.code === 200) {
        const merchant = res.data
        this.basicConfig = {
          merchantName: merchant.merchantName || '',
          contactPhone: merchant.contactPhone || '',
          address: merchant.address || '',
          wechatQrcode: merchant.wechatQrcode || ''
        }
      }
    },

    async loadMerchantConfigs() {
      const res = await getMerchantConfigMapByMerchantId(this.merchantId)
      if (res.code === 200) {
        const configs = res.data || {}

        // 抽奖配置
        this.lotteryConfig = {
          claimInstruction: configs.claim_instruction || '请到前台出示此页面领取奖品',
          dailyDrawLimit: parseInt(configs.daily_draw_limit) || 3,
          activityEnabled: configs.activity_enabled === 'true'
        }

        // UI配置
        const uiConfigStr = configs.ui_config
        if (uiConfigStr) {
          try {
            const uiConfigObj = JSON.parse(uiConfigStr)
            this.uiConfig = {
              primaryColor: uiConfigObj.primaryColor || '#667eea',
              backgroundImage: uiConfigObj.backgroundImage || '',
              logoImage: uiConfigObj.logoImage || '',
              welcomeText: uiConfigObj.welcomeText || '欢迎使用抽奖点餐系统',
              pageTitle: uiConfigObj.pageTitle || '抽奖点餐'
            }
          } catch (e) {
            console.error('UI配置解析失败:', e)
          }
        }

        // 高级配置
        this.advancedConfig = {
          customCss: configs.custom_css || '',
          customJs: configs.custom_js || '',
          analyticsCode: configs.analytics_code || ''
        }
      }
    },

    async saveBasicConfig() {
      this.$refs["basicForm"].validate(async (valid) => {
        if (valid) {
          try {
            const updateData = {
              merchantId: this.merchantId,
              merchantName: this.basicConfig.merchantName,
              contactPhone: this.basicConfig.contactPhone,
              address: this.basicConfig.address,
              wechatQrcode: this.basicConfig.wechatQrcode
            }

            await updateMerchant(updateData)
            this.$modal.msgSuccess("基本配置保存成功")
          } catch (error) {
            this.$modal.msgError("保存失败")
          }
        }
      })
    },

    async saveLotteryConfig() {
      try {
        const configMap = {
          claim_instruction: this.lotteryConfig.claimInstruction,
          daily_draw_limit: this.lotteryConfig.dailyDrawLimit.toString(),
          activity_enabled: this.lotteryConfig.activityEnabled.toString()
        }

        await batchSaveOrUpdateConfig(this.merchantId, configMap)
        this.$modal.msgSuccess("抽奖配置保存成功")
      } catch (error) {
        this.$modal.msgError("保存失败")
      }
    },

    async saveUIConfig() {
      try {
        const configMap = {
          ui_config: JSON.stringify(this.uiConfig)
        }

        await batchSaveOrUpdateConfig(this.merchantId, configMap)
        this.$modal.msgSuccess("UI配置保存成功")
      } catch (error) {
        this.$modal.msgError("保存失败")
      }
    },

    async saveAdvancedConfig() {
      try {
        const configMap = {
          custom_css: this.advancedConfig.customCss,
          custom_js: this.advancedConfig.customJs,
          analytics_code: this.advancedConfig.analyticsCode
        }

        await batchSaveOrUpdateConfig(this.merchantId, configMap)
        this.$modal.msgSuccess("高级配置保存成功")
      } catch (error) {
        this.$modal.msgError("保存失败")
      }
    },

    previewUI() {
      this.previewVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.preview-phone {
  width: 300px;
  height: 500px;
  border: 2px solid #ccc;
  border-radius: 20px;
  overflow: hidden;
  background: #fff;

  .preview-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
  }

  .preview-content {
    height: 440px;
    padding: 20px;
    background-size: cover;
    background-position: center;
    display: flex;
    flex-direction: column;
    align-items: center;

    .preview-logo {
      margin-bottom: 20px;

      img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
      }
    }

    .preview-welcome {
      font-size: 18px;
      margin-bottom: 40px;
      text-align: center;
    }

    .preview-buttons {
      display: flex;
      gap: 20px;

      .preview-btn {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
